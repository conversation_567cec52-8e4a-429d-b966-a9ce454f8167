import { useState, type ReactNode, useEffect } from 'react'
import { Navbar, Nav<PERSON>Brand, NavbarContent, NavbarItem, Button, Badge, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react'
import { ShoppingBasketIcon, UserIcon, LogOutIcon, ClipboardListIcon } from 'lucide-react'
import { navigate } from 'astro:transitions/client'
import SearchBar from './SearchBar'
import { ThemeToggle } from './ThemeToggle'
import { LoginDrawe } from './users/LoginDrawer'
import { cartTotal } from '@/stores/cart'
import { userStore, clearUser, setUser } from '@/stores/user'
import { useStore } from '@tanstack/react-store'
import { useStore as useNanoStore } from '@nanostores/react'
import { formatInitials } from '@/lib/utils'
import { COMPANY_EMAIL, COMPANY_NAME, COMPANY_PHONES } from '@/lib/config'
import { InstantSearchAutocomplete } from './InstantSearchAutocomplete'
import { NavbarDropdownMenu } from './NavbarDropdownMenu'

interface Props {
  isFixed?: boolean
  logoComponent?: React.ReactNode
  isMobile?: boolean
}

export const NavbarMain = ({ isFixed = false, logoComponent, isMobile = false }: Props) => {
  const [userisLoading, setUserisLoading] = useState(true)

  const $cartTotal = useStore(cartTotal)
  const $user = useNanoStore(userStore)

  const [isLargeScreen, setIsLargeScreen] = useState<boolean>(!isMobile)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    setUserisLoading(false)
  }, [])

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', {
        credentials: 'include'
      })
    } catch (error) {
      console.warn('handleLogout:', error)
    }
    document.cookie = 'rti-session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    clearUser()
    navigate('/')
    window.location.reload()
  }

  const UserMenu = () => {
    const userInitials = formatInitials($user.client_name)

    if (!$user.client_id) {
      return (
        <LoginDrawe
          activator={(onOpen) => (
            <Button onPress={onOpen} isIconOnly={!isLargeScreen} size={!isLargeScreen ? 'sm' : 'md'} color='default' aria-label='Войти в профиль'>
              <span className='hidden lg:inline'>Войти</span> <UserIcon aria-hidden='true' />
            </Button>
          )}
        />
      )
    }

    return (
      <Dropdown>
        <DropdownTrigger>
          <Button
            isLoading={userisLoading}
            className='flex items-center gap-2'
            isIconOnly={!isLargeScreen}
            size={!isLargeScreen ? 'sm' : 'md'}
            aria-label='Открыть меню профиля'
            aria-haspopup='menu'
          >
            <UserIcon aria-hidden='true' />
            <span className='hidden lg:inline'>{userInitials || 'Профиль'}</span>
          </Button>
        </DropdownTrigger>
        <DropdownMenu aria-label='Меню пользователя'>
          <DropdownItem key='profile' startContent={<UserIcon />}>
            <a href='/dashboard/settings'>Профиль</a>
          </DropdownItem>
          <DropdownItem key='orders' startContent={<ClipboardListIcon />}>
            <a href='/dashboard/orders'>Заказы</a>
          </DropdownItem>
          <DropdownItem key='logout' className='text-danger' color='danger' startContent={<LogOutIcon />} onPress={handleLogout}>
            Выйти
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    )
  }

  return (
    <>
      <Navbar
        maxWidth='full'
        isBordered
        classNames={{
          base: `bg-zinc-800 dark:bg-zinc-900 sm:py-2 ${isFixed ? ' fixed' : ''}`,
          content: 'sm:py-3',
          wrapper: 'sm:py-10 my-4 px-2 sm:px-6'
        }}
        position='sticky'
      >
        <NavbarContent>
          <NavbarBrand>
            <div className='-mt-1 w-44 lg:w-80'>
              <a href='/' data-astro-prefetch='tap' aria-label='Перейти на главную страницу'>
                {logoComponent}
              </a>
              {isMobile && (
                <div className='mt-1'>
                  <InstantSearchAutocomplete isMobile={true} size='sm' />
                </div>
              )}
            </div>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent className='hidden w-full justify-center gap-4 sm:flex' justify='center'>
          <div className='mt-2 flex w-full items-start justify-center gap-3'>
            <div>
              <NavbarDropdownMenu />
            </div>
            <div className='w-full md:max-w-md'>
              <SearchBar size='lg' isMobile={false} tabPlacement='bottom' />
            </div>
          </div>
        </NavbarContent>

        <NavbarContent justify='end'>
          <div>
            <div className='text-xs text-white sm:text-sm md:text-base'>
              <p>{COMPANY_EMAIL}</p>
              <p>{COMPANY_PHONES.toll_free}</p>
            </div>
            <div className='mt-2 flex gap-2 md:mt-3'>
              <NavbarItem>
                {isMounted && <UserMenu />}
                {!isMounted && <Button isIconOnly={!isLargeScreen} size={!isLargeScreen ? 'sm' : 'md'} isLoading> <UserIcon aria-hidden='true' /></Button>}
              </NavbarItem>
              <NavbarItem>
                <a href='/cart' aria-label='Перейти в корзину'>
                  {isMounted && (
                    <Badge className='font-semibold text-default-100' variant='solid' size={!isLargeScreen ? 'sm' : 'md'} color='warning' content={$cartTotal}>
                      <Button
                        isIconOnly={!isLargeScreen}
                        onClick={(e) => e.preventDefault()}
                        onPress={(e) => navigate('/cart')}
                        size={!isLargeScreen ? 'sm' : 'md'}
                        aria-label='Перейти в корзину'
                      >
                        <span className='hidden lg:block'>Корзина</span> <ShoppingBasketIcon aria-hidden='true' />
                      </Button>
                    </Badge>
                  )}
                  {!isMounted && (
                    <Button
                      isLoading
                      isIconOnly={!isLargeScreen}
                      onClick={(e) => e.preventDefault()}
                      onPress={() => navigate('/cart')}
                      size={!isLargeScreen ? 'sm' : 'md'}
                    />
                  )}
                </a>
              </NavbarItem>
              <NavbarItem className=''>
                <ThemeToggle size={!isLargeScreen ? 'sm' : 'md'} aria-label='Переключить тему' />
                {!isMounted && <Button isIconOnly size={!isLargeScreen ? 'sm' : 'md'} isLoading/>}

              </NavbarItem>
            </div>
          </div>
        </NavbarContent>
      </Navbar>
    </>
  )
}
