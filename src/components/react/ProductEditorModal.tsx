import { memo, useCallback } from 'react'
import { <PERSON>dal, Modal<PERSON>ontent, ModalHeader, ModalBody, Modal<PERSON>ooter, But<PERSON> } from '@heroui/react'
import { useStore } from '@nanostores/react'
import { $productEditor, closeProductEditor } from '@/stores/productEditor'

export const ProductEditorModal = memo(() => {
  const { isOpen, productId } = useStore($productEditor)

  const handleClose = useCallback(() => {
    closeProductEditor()
    window.location.reload()
  }, [])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size='full'
      scrollBehavior='outside'
      backdrop='blur'
      classNames={{
        base: 'bg-background/80',
        body: 'p-0'
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className='flex items-center justify-between'>
              <div className='text-base'>Редактировать #{productId}</div>
            </ModalHeader>
            <ModalBody className='p-0'>
              <div className='mt-2'>
                {productId && (
                  <iframe
                    title='Редактировать'
                    allowFullScreen
                    style={{ border: 'none' }}
                    width='100%'
                    height='800vh'
                    src={`https://a.mirsalnikov.ru/products/${productId}?editormode=1`}
                    id='product-editor-iframe'
                  />
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color='danger' variant='light' onPress={onClose}>
                Закрыть
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  )
})

ProductEditorModal.displayName = 'ProductEditorModal'
