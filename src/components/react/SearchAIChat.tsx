import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Card, CardBody, Textarea, Avatar, Chip, Divider, Spinner, Image, CardFooter, <PERSON>, Al<PERSON> } from '@heroui/react'
import { ExternalLinkIcon, SendIcon, RotateCcwIcon } from 'lucide-react'
import { trpcReact } from '@/trpc'
import { TrpcReactWrapper } from './TrcpReactWrapper'
import { QtyInput } from './QtyInput'
import { getRtiImageUrl } from '@/lib/config'

// Типы на основе реального API
interface ChatProduct {
  prod_id: number
  prod_sku: string
  prod_analogsku: string
  prod_img?: string
  prod_count: number
  prod_purpose?: string
  prod_size?: string
  prod_type?: string
  prod_material?: string
  prod_manuf?: string
  _formatted?: {
    prod_id?: string
    prod_sku?: string
    prod_analogsku?: string
    prod_img?: string
    prod_purpose?: string
    prod_size?: string
    prod_type?: string
    prod_material?: string
    prod_manuf?: string
  }
}

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
  products?: ChatProduct[]
}

interface HistoryMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  products?: ChatProduct[]
}

interface ChatHistoryResponse {
  success: boolean
  threadId: string
  messages?: {
    uiMessages: HistoryMessage[]
  }
}

const highlightHtml = (html: string) => {
  return html?.replace(/_shl_/g, '<span class="bg-warning-100 px-1 rounded">')?.replace(/_ehl_/g, '</span>') || ''
}

export const SearchAIChat = ({ clientInitMessage = '' }) => {
  return (
    <TrpcReactWrapper>
      <Chat clientInitMessage={clientInitMessage} />
    </TrpcReactWrapper>
  )
}

const ProductCard = ({ product: item }: { product: ChatProduct }) => {
  const productId = item._formatted?.prod_id || item.prod_id?.toString()
  const productSku = item._formatted?.prod_sku || item.prod_sku
  const productManuf = item._formatted?.prod_manuf || item.prod_manuf
  const productType = item._formatted?.prod_type || item.prod_type

  const productAnalogSku = item._formatted?.prod_analogsku || item.prod_analogsku
  const productImg = item._formatted?.prod_img || item.prod_img || productAnalogSku

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} className='w-full'>
      <Card className='border dark:border-default-200' shadow='sm'>
        <CardBody className='p-4'>
          {/* Изображение товара */}
          <div className='relative mb-4'>
            <Image
              shadow='sm'
              width='100%'
              height={200}
              fallbackSrc={getRtiImageUrl(`${productImg}.jpg`)}
              className='rounded-lg object-cover'
              loading='lazy'
              src={getRtiImageUrl(`${productImg}.jpg`)}
              alt={productSku}
            />
            {/* Статус наличия */}
            <div className='absolute right-2 top-2 rounded-lg bg-background/80 px-2 py-1 backdrop-blur-sm'>
              <div className='flex items-center gap-1.5'>
                <div className={`h-2 w-2 rounded-full ${item.prod_count > 0 ? 'bg-success-500' : 'bg-default-500'}`} />
                <span className='text-xs font-medium'>{item.prod_count > 0 ? `В наличии: ${item.prod_count}` : 'Предзаказ'}</span>
              </div>
            </div>
          </div>

          {/* Информация о товаре */}
          <div className='mb-4 space-y-2'>
            {/* Артикул */}
            <div className='text-sm'>
              <span className='font-medium text-default-600'>Артикул: </span>
              <span dangerouslySetInnerHTML={{ __html: highlightHtml(productSku) }} />
              {productAnalogSku && productAnalogSku !== productSku && (
                <>
                  {' / '}
                  <span dangerouslySetInnerHTML={{ __html: highlightHtml(productAnalogSku) }} />
                </>
              )}
            </div>

            {/* Назначение */}
            {item.prod_purpose && (
              <div className='text-sm'>
                <span className='font-medium text-default-600'>Назначение: </span>
                <span dangerouslySetInnerHTML={{ __html: highlightHtml(item.prod_purpose) }} />
              </div>
            )}

            {/* Размер */}
            {item.prod_size && (
              <div className='text-sm'>
                <span className='font-medium text-default-600'>Размер: </span>
                <span dangerouslySetInnerHTML={{ __html: highlightHtml(item.prod_size) }} />
              </div>
            )}

            {/* Бренд */}
            {item.prod_manuf && (
              <div className='text-sm'>
                <span className='font-medium text-default-600'>Бренд: </span>
                <span dangerouslySetInnerHTML={{ __html: highlightHtml(item.prod_manuf) }} />
              </div>
            )}

            {/* Тип/Материал */}
            {(item.prod_type || item.prod_material) && (
              <div className='text-sm'>
                <span className='font-medium text-default-600'>Тип/Материал: </span>
                {item.prod_type && <span dangerouslySetInnerHTML={{ __html: highlightHtml(item.prod_type) }} />}
                {item.prod_type && item.prod_material && ' / '}
                {item.prod_material && <span dangerouslySetInnerHTML={{ __html: highlightHtml(item.prod_material) }} />}
              </div>
            )}
          </div>

          {/* Кнопки действий */}
          <div className='flex flex-col gap-2'>
            <QtyInput label='В корзину' size='sm' product={{ ...item, prod_id: Number(productId) }} />
            <Link
              className='flex items-center justify-center gap-2 rounded-lg bg-default-100 p-2 text-sm transition-colors hover:bg-default-200'
              target='_blank'
              color='foreground'
              href={`/catalog/product/${productId}`}
            >
              <span>Подробнее</span>
              <ExternalLinkIcon className='h-4 w-4' />
            </Link>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  )
}

const MessageBubble = ({ response }: { response: ChatMessage }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.2 }}
    className={`flex ${response.isUser ? 'justify-end' : 'justify-start'} mb-4`}
  >
    <div className={`flex max-w-[85%] items-start gap-3 md:max-w-[70%] ${response.isUser ? 'flex-row-reverse' : 'flex-row'}`}>
      <Avatar size='sm' name={response.isUser ? 'Вы' : 'AI'} color={response.isUser ? 'primary' : 'secondary'} className='flex-shrink-0' />
      <Card className={`${response.isUser ? 'bg-primary-100 dark:bg-primary-900/20' : 'bg-default-100'} shadow-sm`}>
        <CardBody className='p-3'>
          <div className='aimessage text-sm leading-relaxed' dangerouslySetInnerHTML={{ __html: response.text }} />
          <p className='mt-2 text-xs text-default-400'>
            {response.timestamp.toLocaleTimeString('ru-RU', {
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </CardBody>
      </Card>
    </div>
  </motion.div>
)

const Chat = ({ clientInitMessage = '' }) => {
  // TRPC хуки
  const { isPending: isLoading, mutateAsync: sendMessage } = trpcReact.ai.aiChat.useMutation()

  const { refetch: generateNewThread } = trpcReact.ai.generateChatThread.useQuery(undefined, { enabled: false })
  const { isPending: isDeletingHistory, mutateAsync: deleteHistory } = trpcReact.ai.deleteChatHistory.useMutation()

  // Состояние компонента
  const [messages, setMessages] = useState<ChatMessage[]>(clientInitMessage ? [] : [
    {
      id: '1',
      text: 'Здравствуйте! Я помогу вам найти резинотехнические изделия. Что вы ищете?',
      isUser: false,
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [currentProducts, setCurrentProducts] = useState<ChatProduct[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // !window.location.href.includes('initialized')
  useEffect(() => {
    if (isInitialized && clientInitMessage?.length >= 4 && messages.length < 2) {
      handleSendMessage(clientInitMessage)
    }
  }, [clientInitMessage, isInitialized])
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Функция для загрузки истории чата
  const loadChatHistory = async (threadId: string): Promise<boolean> => {
    try {
      setIsLoadingHistory(true)
      const response = await fetch(`/api/trpc/ai.getChatHistory?input=${encodeURIComponent(JSON.stringify({ threadId }))}`, {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to fetch chat history')
      }

      const data = await response.json()
      const history: ChatHistoryResponse = data.result.data

      if (history.success && history.messages?.uiMessages && history.messages.uiMessages.length > 0) {
        // Преобразуем историю в формат компонента
        const convertedMessages: ChatMessage[] = history.messages.uiMessages.map((msg: HistoryMessage, index: number) => ({
          id: msg.id || `${index}`,
          text: msg.content,
          isUser: msg.role === 'user',
          timestamp: new Date(msg.createdAt),
          products: msg.products || undefined
        }))

        setMessages(convertedMessages)
        return true
      }
      return false
    } catch (error) {
      console.warn('Не удалось загрузить историю чата:', error)
      return false
    } finally {
      setIsLoadingHistory(false)
    }
  }

  // Функция для создания нового треда
  const createNewThread = async (): Promise<string | null> => {
    try {
      const result = await generateNewThread()
      return result.data?.threadId || null
    } catch (error) {
      console.error('Ошибка создания нового треда:', error)
      return null
    }
  }

  const getThreadIdFromUrl = () => {
    const qs = new URLSearchParams(window.location.search)
    return qs.get('threadId')
  }

  const savedThreadIdToUrl = (id: string) => {
    if (!id) {
      return
    }
    const u = new URL(window.location.href)
    u.searchParams.set('threadId', id)
    u.searchParams.set('initialized', '1')
    window.history.replaceState(null, '', u)

  }

  // Инициализация чата при загрузке компонента
  useEffect(() => {
    const initializeChat = async () => {
      try {
        const savedThreadId = getThreadIdFromUrl() //|| localStorage.getItem('chatThreadId')

        if (savedThreadId) {
          // Пытаемся загрузить историю
          const historyLoaded = await loadChatHistory(savedThreadId)
          if (historyLoaded) {
            setCurrentThreadId(savedThreadId)
            setIsInitialized(true)
            return
          } else {
            // Удаляем неработающий threadId
            localStorage.removeItem('chatThreadId')
          }
        }

        // Если истории нет или она не загрузилась, создаем новый тред
        const newThreadId = await createNewThread()
        if (newThreadId) {
          setCurrentThreadId(newThreadId)
          localStorage.setItem('chatThreadId', newThreadId)
          savedThreadIdToUrl(newThreadId)
        }
        setIsInitialized(true)
      } catch (error) {
        console.error('Ошибка инициализации чата:', error)
        setIsInitialized(true) // Все равно помечаем как инициализированный
      }
    }

    initializeChat()
  }, [generateNewThread])

  const handleSendMessage = async (customMessage = '') => {
    
    const messageText = customMessage?.trim() || inputValue.trim()
    
    // Исправленное условие - проверяем итоговый текст сообщения
    if (!messageText || isLoading || !isInitialized) return
    console.log("🚀 ~ handleSendMessage ~ customMessage:", customMessage)
    
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageText, // ✅ Используем правильный текст
      isUser: true,
      timestamp: new Date()
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue('')

    try {
      const response = await sendMessage({
        message: messageText, // ✅ Отправляем правильный текст
        threadId: currentThreadId || undefined
      })

      // Обновляем threadId если он изменился
      if (response.threadId && response.threadId !== currentThreadId) {
        setCurrentThreadId(response.threadId)
        localStorage.setItem('chatThreadId', response.threadId)
      }

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response.response,
        isUser: false,
        timestamp: new Date(),
        products: response.success && 'products' in response ? response.products : undefined
      }

      setMessages((prev) => [...prev, aiMessage])
      setCurrentProducts((response as any).products || [])
    } catch (error) {
      console.error('Ошибка отправки сообщения:', error)
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: 'Извините, произошла ошибка. Попробуйте еще раз.',
        isUser: false,
        timestamp: new Date()
      }
      setMessages((prev) => [...prev, errorMessage])
    }
  }

  const handleNewChat = async () => {
    try {
      const newThreadId = await createNewThread()
      if (newThreadId) {
        setCurrentThreadId(newThreadId)
        localStorage.setItem('chatThreadId', newThreadId)

        window.history.replaceState(null, '', window.location.origin)


        // Сбрасываем сообщения к начальному состоянию
        setMessages([
          {
            id: '1',
            text: 'Привет! Я помогу вам найти резинотехнические изделия и сальники для вашего автомобиля. Что вы ищете?',
            isUser: false,
            timestamp: new Date()
          }
        ])
        setCurrentProducts([])
      }
    } catch (error) {
      console.error('Ошибка создания нового чата:', error)
    }
  }

  const handleClearHistory = async () => {
    if (!currentThreadId) return

    try {
      await deleteHistory({ threadId: currentThreadId })
      localStorage.removeItem('chatThreadId')

      // Создаем новый чат
      await handleNewChat()
    } catch (error) {
      console.error('Ошибка очистки истории:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Показываем загрузку пока чат не инициализирован
  if (!isInitialized) {
    return (
      <div className='flex h-96 items-center justify-center'>
        <div className='flex items-center gap-2'>
          <Spinner size='sm' color='primary' />
          <span className='text-sm text-default-500'>Инициализация чата...</span>
        </div>
      </div>
    )
  }

  return (
    <div className='mx-auto flex max-w-7xl flex-col gap-6 p-4 lg:flex-row'>
      {/* Блок чата */}
      <div className='flex flex-1 flex-col overflow-hidden rounded-lg border border-default-200 bg-background shadow-sm'>
        {/* Заголовок с кнопками управления */}
        <div className='flex flex-col items-start justify-between gap-3 border-b border-default-200 bg-default-50 p-4 sm:flex-row sm:items-center'>
          <div className='flex items-center gap-3'>
            <h3 className='text-lg font-semibold'>AI Чат</h3>
            {currentThreadId && (
              <Chip size='sm' variant='flat' color='primary' className='hidden sm:flex'>
                ID: {currentThreadId.slice(-8)}
              </Chip>
            )}
          </div>
          <div className='flex w-full items-center gap-2 sm:w-auto'>
            <Alert color="primary" className='text-sm p-1'>Время генерации ответа: ~7 секунд.</Alert>

            <Button
              size='sm'
              variant='flat'
              color='warning'
              onPress={handleNewChat}
              startContent={<RotateCcwIcon className='h-4 w-4' />}
              className='flex-1 sm:flex-initial'
            >
              Новый чат
            </Button>
            <Button
              size='sm'
              variant='flat'
              color='danger'
              onPress={handleClearHistory}
              isLoading={isDeletingHistory}
              isDisabled={!currentThreadId}
              className='flex-1 sm:flex-initial'
            >
              Очистить
            </Button>
          </div>
        </div>

        {/* Область сообщений */}
        <div className='flex-1 overflow-y-auto px-4 py-4' style={{ height: 'calc(100vh - 300px)', minHeight: '400px' }}>
          {messages.map((response) => (
            <MessageBubble key={response.id} response={response} />
          ))}

          {/* Индикатор загрузки */}
          {isLoading && (
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className='mb-4 flex justify-start'>
              <div className='flex items-start gap-3'>
                <Avatar size='sm' name='AI' color='secondary' className='flex-shrink-0' />
                <Card className='bg-default-100 shadow-sm'>
                  <CardBody className='p-3'>
                    <div className='flex items-center gap-2'>
                      <Spinner size='sm' color='primary' />
                      <span className='text-sm text-default-500'>Работает...</span>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </motion.div>
          )}

          {/* Индикатор загрузки истории */}
          {isLoadingHistory && (
            <div className='flex justify-center py-4'>
              <Spinner size='sm' color='primary' />
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Форма ввода */}
        <div className='border-t border-default-200 bg-default-50 p-4'>
          <div className='flex gap-2'>
            <Textarea
              placeholder='Введите ваш вопрос о товарах...'
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              minRows={1}
              maxRows={4}
              className='flex-1'
              variant='bordered'
            />
            <Button color='primary' onPress={handleSendMessage} isDisabled={isLoading || !inputValue.trim()} isIconOnly className='h-auto min-h-[40px]'>
              <SendIcon className='h-5 w-5' />
            </Button>
          </div>
        </div>
      </div>

      {/* Блок товаров */}
      {currentProducts.length > 0 && (
        <div className='w-full lg:w-96'>
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.5 }} className='sticky top-4'>
            <h4 className='mb-4 text-lg font-semibold'>Найденные товары ({currentProducts.length})</h4>
            <div className='max-h-[calc(100vh-150px)] space-y-4 overflow-y-auto pr-2'>
              {currentProducts.map((product, index) => (
                <ProductCard key={product?.prod_id || `product-${index}`} product={product} />
              ))}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}
